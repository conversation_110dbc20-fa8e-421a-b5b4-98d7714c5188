import json
import subprocess
import shlex
import textwrap
import re

def normalize_lyrics(text: str, max_chars: int = 25) -> str:
    """
    Wrap text to max_chars per line, preserving word boundaries and indentation.
    Returns a string with '\\n' separators suitable for ffmpeg drawtext.
    """
    # Detect leading whitespace for indentation
    indent_match = re.match(r'\s*', text)
    indent = indent_match.group(0) if indent_match else ''
    stripped = text.strip()
    wrapper = textwrap.TextWrapper(
        width=max_chars,
        initial_indent=indent,
        subsequent_indent=indent,
        break_long_words=False,
        break_on_hyphens=False
    )
    lines = wrapper.wrap(stripped)
    return lines


def ffprobe_duration(path):
    """Return duration in seconds (float) of an audio/video file via ffprobe."""
    cmd = [
        "ffprobe", "-v", "error",
        "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1",
        path
    ]
    out = subprocess.check_output(cmd).strip()
    return float(out)

def parse_time(ts):
    """
    Parse timestamps like "0:08:219" (m:ss:ms) into seconds (float).
    Assumes ms is 3-digit milliseconds.
    """
    m, s, ms = ts.split(":")
    return int(m) * 60 + int(s) + int(ms) / 1000.0

def create_lyric_video(
    audio_path: str,
    user_image_path: str,
    border_path: str,
    brand_logo_path: str,
    lyrics_json_path: str,
    track_name: str,
    username: str,
    original_artist: str,
    output_path: str,
    video_waveform_path: str,
    font_path: str = "/path/to/Ethnocentric.ttf"
):
    # 1) Get audio duration
    duration = ffprobe_duration(audio_path)

    # 2) Load lyrics JSON
    with open(lyrics_json_path, "r") as f:
        lyrics_data = json.load(f)["lyrics"]["data"]

    # 3) Video geometry & positions
    BG_W, BG_H = 1080, 1920
    IMG_SIZE    = 240
    USER_X      = (BG_W - IMG_SIZE) // 6
    USER_Y      = int(BG_H * 0.15) - IMG_SIZE // 2

    LOGO_SIZE = 120
    LOGO_X    = USER_X + IMG_SIZE - LOGO_SIZE//2 - 40
    LOGO_Y    = USER_Y + IMG_SIZE - LOGO_SIZE//2 - 20

    TEXT_X    = (BG_W - IMG_SIZE) / 1.7
    TEXT_Y    = USER_Y + 40
    LYRICS_Y  = int(BG_H * 0.15)

    fc = []

    # 4a) Black background of same duration
    fc.append(f"color=black:s={BG_W}x{BG_H}:d={duration}[bg]")

    # 4b) Mask user image into a circle (RGBA)
    fc.append(
        f"[1:v]scale={IMG_SIZE}:{IMG_SIZE},format=yuva444p,"
        "geq="
          "lum='p(X,Y)':"
          "a='if(lte(pow(X-(W/2),2)+pow(Y-(H/2),2),pow(min(W,H)/2,2)),255,0)'"
        "[usercircle]"
    )

    # 4c) Overlay the flat JPEG border
    fc.append(f"[2:v]scale={IMG_SIZE+20}:{IMG_SIZE+20}[border]")
    fc.append(f"[bg][border]overlay=x={USER_X-10}:y={USER_Y-10}[step0]")

    # 4d) Overlay user-circle on top of border
    fc.append(f"[step0][usercircle]overlay=x={USER_X}:y={USER_Y}[step1]")

    # 4e) Scale & overlay brand logo
    fc.append(f"[3:v]scale={LOGO_SIZE}:{LOGO_SIZE}[logo]")
    fc.append(f"[step1][logo]overlay=x={LOGO_X}:y={LOGO_Y}[step2]")

    # 4e2) Add looped video waveform - vertically centered, 100% width, 25% height
    waveform_height = int(BG_H * 0.25)  # 25% of video height
    waveform_y = (BG_H - waveform_height) // 2  # vertically centered
    fc.append(f"[4:v]scale={BG_W}:{waveform_height},loop=loop=-1:size=1:start=0[waveform]")
    fc.append(f"[step2][waveform]overlay=x=0:y={waveform_y}:shortest=1[step2_waveform]")

    # 4f) Draw track title, “COVER BY”, and username
    fc.append(
        f"[step2_waveform]drawtext="
        f"fontfile='{font_path}':"
        f"text='{track_name}':"
        "fontcolor=#FEFEFECC:"  # white @80% alpha
        "fontsize=40:"
        f"x={TEXT_X}:y={TEXT_Y}"
        "[tt1]"
    )
    fc.append(
        "[tt1]drawtext="
        # f"fontfile='{font_path}':"
        f"text='{original_artist.upper()}':"
        "fontcolor=#FEFEFECC:"
        "fontsize=34:"
        f"x={TEXT_X}:y={TEXT_Y + 50}"
        "[tt2]"
    )
    fc.append(
        "[tt2]drawtext="
        # f"fontfile='{font_path}':"
        f"text='{username}':"
        "fontcolor=#FEFEFECC:"
        "fontsize=36:"
        f"x={TEXT_X}:y={TEXT_Y + 140}"
        "[step3]"
    )

    # 4g) Draw each lyric line (light-pink @80%), wrapped & timed
    last_label = "step3"
    LINE_HEIGHT = 60
    FONT_SIZE   = 50

    for idx, item in enumerate(lyrics_data, start=1):
        start_s = parse_time(item["start_time"])
        end_s   = parse_time(item["end_time"])
        raw     = item["text"]

        lines = normalize_lyrics(raw, max_chars=25)
        label = last_label

        for j, line_text in enumerate(lines):
            esc   = line_text.replace("'", r"\'")
            y_pos = LYRICS_Y + j * LINE_HEIGHT
            new_label = f"lyric{idx}_{j}"

            fc.append(
                f"[{label}]drawtext="
                f"fontfile='{font_path}':"
                f"text='{esc}':"
                "fontcolor=#E955BBCC:"  # rgba(233,85,187,0.8)
                f"fontsize={FONT_SIZE}:"
                f"x=(w-text_w)/2:y={y_pos}:"
                f"enable='between(t,{start_s},{end_s})'"
                f"[{new_label}]"
            )
            label = new_label

        last_label = label

    # 5) Build & run the ffmpeg command
    cmd = [
        "ffmpeg",
        "-y",
        "-f", "lavfi", "-i", "anullsrc=channel_layout=stereo:sample_rate=48000",
        "-i", user_image_path,
        "-i", border_path,
        "-i", brand_logo_path,
        "-i", video_waveform_path,
        "-i", audio_path,
        "-filter_complex", ";".join(fc),
        "-map", f"[{last_label}]",   # final video
        "-map", "5:a",               # real audio (now index 5)
        "-c:v", "libx264",
        "-preset", "medium",
        "-crf", "23",
        "-pix_fmt", "yuv420p",
        "-c:a", "aac",
        "-b:a", "128k",
        output_path
    ]

    print("Running ffmpeg:\n", " ".join(shlex.quote(x) for x in cmd))
    subprocess.run(cmd, check=True)

if __name__ == "__main__":
    audio_path = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/lyrical_video_est/lean_on.flac"
    user_image_path = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/lyrical_video_est/singer.jpeg"
    border_path = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/lyrical_video_est/profile_border.png"
    brand_logo_path = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/lyrical_video_est/logo_2.png"
    lyrics_json_path = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/lyrical_video_est/lean_on_88.json"
    video_waveform_path= "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/lyrical_video_est/video_waveform.mp4"
    track_name = "My Awesome Track"
    username = "Artist Name"
    output_path = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/outputs/music_video.mp4"
    create_lyric_video(
        audio_path=audio_path,
        user_image_path=user_image_path,
        border_path=border_path,
        brand_logo_path=brand_logo_path,
        lyrics_json_path=lyrics_json_path,
        video_waveform_path=video_waveform_path,
        track_name="Lean On",
        username="Roxi Fox",
        original_artist="Major Lazer",
        output_path=output_path,
        font_path="/Users/<USER>/animesh-melodyze/ffmpeg-experiments/ethnocentric_rg.otf"
    )